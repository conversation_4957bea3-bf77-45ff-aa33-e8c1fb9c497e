require 'active_record'

class DatabaseHelper
  # Simple DB seeding to avoid bringing in other gems like FactoryGirl etc.
  # This does the job for an example seeder
  def self.seed!
    user_data = [
      {id: 11, first_name: '<PERSON>', last_name: '<PERSON><PERSON><PERSON>', uuid: 'deadbeef', email: '<EMAIL>'},
      {id: 12, first_name: '<PERSON>', last_name: '<PERSON><PERSON><PERSON>', uuid: 'beefdead', email: '<EMAIL>'},
      {id: 13, first_name: '<PERSON>', last_name: '<PERSON><PERSON>', uuid: 'beefbeef', email: '<EMAIL>'},
    ]
    (100..199).each do |i|
      user_data.push id: i, first_name: "User-#{i}", last_name: "Last-#{i}", uuid: SecureRandom.hex(16).to_s
    end
    user_data.each_with_index do |data, i|
      ::User.create(**data)
    end
    puts "Database seeded."
  end
end