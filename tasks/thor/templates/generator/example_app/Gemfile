# frozen_string_literal: true

source 'https://rubygems.org'

gem 'activerecord'
gem 'link_header' # For pagination extensions
gem 'parslet' # For field selection extension
gem 'praxis'
gem 'puma' # A much better web server than the default webrick
gem 'rack'
gem 'sqlite3'

group :development, :test do
  gem 'database_cleaner' # For transactional DB tests
  gem 'rack-test'
  gem 'rake'
  gem 'rspec' # needed for rake task
  gem 'webrick' # For serving the API documentation browser preview

  gem 'pry'
  gem 'pry-byebug'
end
