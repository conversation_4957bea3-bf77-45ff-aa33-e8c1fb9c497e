# require:
#   - rubocop-thread_safety

AllCops:
  Exclude:
    - "_site/**/*"
    - "coverage/**/*"
    - "docs_docusaurus/**/*"
    - "tmp/**/*"
    - "pkg/**/*"
    - "vendor/cache/**/*"
    - "tasks/thor/templates/**/*"
  TargetRubyVersion: 2.7
  NewCops: disable

Metrics/BlockLength:
  Enabled: false

Metrics/AbcSize:
  Enabled: false

Metrics/ClassLength:
  Enabled: false

Metrics/ModuleLength:
  Enabled: false

Metrics/CyclomaticComplexity:
  Enabled: false

Metrics/PerceivedComplexity:
  Enabled: false

Metrics/MethodLength:
  Enabled: false  

Layout/LineLength:
  Enabled: false  

Style/Documentation:
  Enabled: false

#### RE-ENABLE PLEASE
Metrics/BlockNesting:
  Enabled: false

Style/OptionalBooleanParameter:
  Enabled: false
  
Lint/Void:
  Enabled: false
  
Lint/MissingSuper:
  Enabled: false    