# frozen_string_literal: true

class PaymentMethod < ActiveRecord::Base
  belongs_to :user
  has_many :payments, dependent: :destroy

  validates :uuid, presence: true, uniqueness: true
  validates :payment_type, presence: true, inclusion: { in: %w[credit_card paypal bank_account] }
  validates :status, inclusion: { in: %w[active expired disabled] }

  scope :active, -> { where(status: 'active') }
  scope :default, -> { where(is_default: true) }

  def display_name
    case payment_type
    when 'credit_card'
      "#{brand.capitalize} ending in #{last_four}"
    when 'paypal'
      'PayPal Account'
    when 'bank_account'
      "Bank Account ending in #{last_four}"
    else
      payment_type.humanize
    end
  end

  def masked_number
    last_four ? "**** **** **** #{last_four}" : "****"
  end
end
