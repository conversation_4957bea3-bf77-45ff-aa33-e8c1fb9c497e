# frozen_string_literal: true

class Payment < ActiveRecord::Base
  belongs_to :user
  belongs_to :payment_method

  validates :uuid, presence: true, uniqueness: true
  validates :amount_cents, presence: true, numericality: { greater_than: 0 }
  validates :currency, presence: true
  validates :status, presence: true, inclusion: { in: %w[pending completed failed refunded] }

  scope :completed, -> { where(status: 'completed') }
  scope :pending, -> { where(status: 'pending') }
  scope :failed, -> { where(status: 'failed') }

  def amount_dollars
    amount_cents / 100.0
  end

  def formatted_amount
    "$#{'%.2f' % amount_dollars} #{currency}"
  end

  def success?
    status == 'completed'
  end

  def can_refund?
    status == 'completed'
  end
end
