
class User < ActiveRecord::Base
  # So it can be used in all the automatic query/filtering extensions
  include Praxis::Mapper::ActiveModelCompat

  has_many :payment_methods, dependent: :destroy
  has_many :payments, dependent: :destroy

  validates :uuid, presence: true, uniqueness: true
  validates :first_name, presence: true
  validates :email, format: { with: URI::MailTo::EMAIL_REGEXP }, allow_blank: true

  def full_name
    "#{first_name} #{last_name}".strip
  end

  def default_payment_method
    payment_methods.where(is_default: true).first
  end

  def total_payments_amount
    payments.where(status: 'completed').sum(:amount_cents) / 100.0
  end
end
