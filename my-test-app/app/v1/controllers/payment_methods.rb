# frozen_string_literal: true

class V1::Controllers::PaymentMethods
  include V1::Concerns::ControllerBase
  include Praxis::Controller

  implements V1::Endpoints::PaymentMethods

  def index
    user = User.find(request.params.user_id)
    payment_methods = user.payment_methods.active
    display(payment_methods)
  end

  def show
    user = User.find(request.params.user_id)
    payment_method = user.payment_methods.find(request.params.id)
    display(payment_method)
  end

  def create
    user = User.find(request.params.user_id)
    
    # Simulate payment method creation (in real app, you'd integrate with Stripe/PayPal)
    payment_method = user.payment_methods.build(request.payload.to_h)
    payment_method.uuid = SecureRandom.uuid
    
    if payment_method.save
      response.status = 201
      display(payment_method)
    else
      response.status = 422
      display(errors: payment_method.errors.full_messages)
    end
  end

  def update
    user = User.find(request.params.user_id)
    payment_method = user.payment_methods.find(request.params.id)
    
    if payment_method.update(request.payload.to_h)
      display(payment_method)
    else
      response.status = 422
      display(errors: payment_method.errors.full_messages)
    end
  end

  def delete
    user = User.find(request.params.user_id)
    payment_method = user.payment_methods.find(request.params.id)
    
    payment_method.update(status: 'disabled')
    response.status = 204
  end
end
