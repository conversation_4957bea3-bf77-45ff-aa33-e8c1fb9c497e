# frozen_string_literal: true

class V1::Controllers::Payments
  include V1::Concerns::ControllerBase
  include Praxis::Controller

  implements V1::Endpoints::Payments

  def index
    user = User.find(request.params.user_id)
    payments = user.payments.includes(:payment_method)
    
    # Apply status filter if provided
    if request.params.status
      payments = payments.where(status: request.params.status)
    end
    
    display(payments.order(created_at: :desc))
  end

  def show
    user = User.find(request.params.user_id)
    payment = user.payments.includes(:payment_method).find(request.params.id)
    display(payment)
  end

  def create
    user = User.find(request.params.user_id)
    payment_method = user.payment_methods.find(request.payload.payment_method_id)
    
    # Simulate payment processing
    payment = user.payments.build(request.payload.to_h.except(:payment_method_id))
    payment.payment_method = payment_method
    payment.uuid = SecureRandom.uuid
    payment.transaction_id = "txn_#{SecureRandom.hex(8)}"
    
    # Simulate payment processing logic
    if simulate_payment_processing(payment)
      payment.status = 'completed'
    else
      payment.status = 'failed'
    end
    
    if payment.save
      response.status = 201
      display(payment)
    else
      response.status = 422
      display(errors: payment.errors.full_messages)
    end
  end

  def refund
    user = User.find(request.params.user_id)
    payment = user.payments.find(request.params.id)
    
    if payment.can_refund?
      payment.update(status: 'refunded')
      display(payment)
    else
      response.status = 422
      display(errors: ['Payment cannot be refunded'])
    end
  end

  private

  def simulate_payment_processing(payment)
    # Simulate 90% success rate
    rand(10) < 9
  end
end
