<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Praxis API Tester</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .panel { border: 1px solid #ddd; padding: 20px; border-radius: 8px; }
        .panel h2 { margin-top: 0; color: #333; }
        input, select, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .response { background: #f5f5f5; padding: 15px; border-radius: 4px; white-space: pre-wrap; font-family: monospace; }
        .examples { background: #e8f4fd; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .examples h3 { margin-top: 0; }
    </style>
</head>
<body>
    <h1>🚀 Praxis API Tester</h1>
    
    <div class="examples">
        <h3>📋 Available Endpoints:</h3>
        <ul>
            <li><strong>GET /users</strong> - Get all users</li>
            <li><strong>GET /users?fields=id,first_name</strong> - Get users with specific fields</li>
            <li><strong>POST /users</strong> - Create a new user</li>
        </ul>
    </div>

    <div class="container">
        <div class="panel">
            <h2>🔧 API Request</h2>
            <label>Method:</label>
            <select id="method">
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
            </select>
            
            <label>Endpoint:</label>
            <input type="text" id="endpoint" value="/users" placeholder="/users">
            
            <label>Query Parameters (e.g., fields=id,first_name):</label>
            <input type="text" id="params" placeholder="fields=id,first_name">
            
            <label>Request Body (JSON):</label>
            <textarea id="body" rows="6" placeholder='{"first_name": "Test", "last_name": "User", "email": "<EMAIL>", "uuid": "123"}'></textarea>
            
            <button onclick="makeRequest()">Send Request</button>
            
            <div style="margin-top: 20px;">
                <h3>Quick Examples:</h3>
                <button onclick="loadExample('all')">Get All Users</button>
                <button onclick="loadExample('fields')">Get Specific Fields</button>
                <button onclick="loadExample('create')">Create User</button>
            </div>
        </div>
        
        <div class="panel">
            <h2>📄 Response</h2>
            <div id="response" class="response">Click "Send Request" to see the response...</div>
        </div>
    </div>

    <script>
        const baseUrl = 'http://localhost:3000';
        
        function loadExample(type) {
            const method = document.getElementById('method');
            const endpoint = document.getElementById('endpoint');
            const params = document.getElementById('params');
            const body = document.getElementById('body');
            
            switch(type) {
                case 'all':
                    method.value = 'GET';
                    endpoint.value = '/users';
                    params.value = '';
                    body.value = '';
                    break;
                case 'fields':
                    method.value = 'GET';
                    endpoint.value = '/users';
                    params.value = 'fields=id,first_name,email';
                    body.value = '';
                    break;
                case 'create':
                    method.value = 'POST';
                    endpoint.value = '/users';
                    params.value = '';
                    body.value = JSON.stringify({
                        "first_name": "Test",
                        "last_name": "User", 
                        "email": "<EMAIL>",
                        "uuid": "test-123"
                    }, null, 2);
                    break;
            }
        }
        
        async function makeRequest() {
            const method = document.getElementById('method').value;
            const endpoint = document.getElementById('endpoint').value;
            const params = document.getElementById('params').value;
            const body = document.getElementById('body').value;
            const responseDiv = document.getElementById('response');
            
            let url = baseUrl + endpoint;
            if (params && method === 'GET') {
                url += '?' + params;
            }
            
            const options = {
                method: method,
                headers: {
                    'X-Api-Version': '1',
                    'Content-Type': 'application/json'
                }
            };
            
            if (body && method !== 'GET') {
                options.body = body;
            }
            
            try {
                responseDiv.textContent = 'Loading...';
                const response = await fetch(url, options);
                const data = await response.text();
                
                responseDiv.textContent = `Status: ${response.status} ${response.statusText}\n\n${data}`;
            } catch (error) {
                responseDiv.textContent = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
