# frozen_string_literal: true

module V1
  module MediaTypes
    class User < Praxis::MediaType
      identifier 'application/json'

      domain_model 'V1::Resources::User'
      description 'A user in the system'

      attributes do
        attribute :id, Integer
        attribute :uuid, String
        attribute :email, String
        attribute :first_name, String
        attribute :last_name, String
        attribute :full_name, String, description: 'Full name of the user'
        attribute :total_payments_amount, Float, description: 'Total amount of completed payments'

        # Associated records
        attribute :payment_methods, Praxis::Collection.of(V1::MediaTypes::PaymentMethod)
        attribute :payments, Praxis::Collection.of(V1::MediaTypes::Payment)
        attribute :default_payment_method, V1::MediaTypes::PaymentMethod
      end


    end
  end
end

