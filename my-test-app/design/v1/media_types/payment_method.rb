# frozen_string_literal: true

module V1
  module MediaTypes
    class PaymentMethod < Praxis::MediaType
  description 'Payment Method representation'

  attributes do
    attribute :id, Integer
    attribute :uuid, String
    attribute :payment_type, String, description: 'Payment method type (credit_card, paypal, bank_account)'
    attribute :last_four, String, description: 'Last four digits of card/account'
    attribute :brand, String, description: 'Card brand or payment provider'
    attribute :is_default, Attributor::<PERSON><PERSON><PERSON>, description: 'Whether this is the default payment method'
    attribute :status, String, description: 'Payment method status'
    attribute :display_name, String, description: 'Human-readable payment method name'
    attribute :masked_number, String, description: 'Masked card/account number'
    attribute :created_at, DateTime
    attribute :updated_at, DateTime

    # Associated user
    attribute :user, V1::MediaTypes::User
  end
    end
  end
end
