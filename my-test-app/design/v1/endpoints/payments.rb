# frozen_string_literal: true

module V1
  module Endpoints
    class Payments
      include Praxis::EndpointDefinition

  description 'Payments API'

  media_type V1::MediaTypes::Payment

  routing do
    prefix '/users/:user_id/payments'
  end

  action :index do
    description 'List payments for a user'
    routing { get '' }
    
    params do
      attribute :user_id, Integer, required: true
      attribute :status, String, values: %w[pending completed failed refunded]
    end

    response :ok, media_type: Praxis::Collection.of(V1::MediaTypes::Payment)
  end

  action :show do
    description 'Get a specific payment'
    routing { get '/:id' }
    
    params do
      attribute :user_id, Integer, required: true
      attribute :id, Integer, required: true
    end

    response :ok
  end

  action :create do
    description 'Process a new payment'
    routing { post '' }
    
    params do
      attribute :user_id, Integer, required: true
    end

    payload do
      attribute :payment_method_id, Integer, required: true
      attribute :amount_cents, Integer, required: true, min: 1
      attribute :currency, String, default: 'USD'
      attribute :description, String
    end

    response :created
    response :unprocessable_entity
  end

  action :refund do
    description 'Refund a payment'
    routing { post '/:id/refund' }
    
    params do
      attribute :user_id, Integer, required: true
      attribute :id, Integer, required: true
    end

    response :ok
    response :unprocessable_entity
  end
    end
  end
end
