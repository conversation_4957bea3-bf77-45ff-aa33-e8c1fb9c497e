# frozen_string_literal: true

module V1
  module Endpoints
    class PaymentMethods
      include Praxis::EndpointDefinition

  description 'Payment Methods API'

  media_type V1::MediaTypes::PaymentMethod

  routing do
    prefix '/users/:user_id/payment_methods'
  end

  action :index do
    description 'List payment methods for a user'
    routing { get '' }
    
    params do
      attribute :user_id, Integer, required: true
    end

    response :ok, media_type: Praxis::Collection.of(V1::MediaTypes::PaymentMethod)
  end

  action :show do
    description 'Get a specific payment method'
    routing { get '/:id' }
    
    params do
      attribute :user_id, Integer, required: true
      attribute :id, Integer, required: true
    end

    response :ok
  end

  action :create do
    description 'Add a new payment method'
    routing { post '' }
    
    params do
      attribute :user_id, Integer, required: true
    end

    payload do
      attribute :payment_type, String, required: true, values: %w[credit_card paypal bank_account]
      attribute :last_four, String
      attribute :brand, String
      attribute :is_default, Attributor::Boolean, default: false
    end

    response :created
    response :unprocessable_entity
  end

  action :update do
    description 'Update a payment method'
    routing { put '/:id' }
    
    params do
      attribute :user_id, Integer, required: true
      attribute :id, Integer, required: true
    end

    payload do
      attribute :is_default, Attributor::Boolean
      attribute :status, String, values: %w[active expired disabled]
    end

    response :ok
    response :unprocessable_entity
  end

  action :delete do
    description 'Disable a payment method'
    routing { delete '/:id' }
    
    params do
      attribute :user_id, Integer, required: true
      attribute :id, Integer, required: true
    end

    response :no_content
  end
    end
  end
end
