#!/usr/bin/env ruby

require 'bundler'
Bundler.setup(:default, 'development')
Bundler.require(:default, 'development')

# Connect to SQLite database
ActiveRecord::Base.establish_connection(
  adapter:  'sqlite3',
  database: "development.sqlite3"
)

# Create the tables
ActiveRecord::Schema.define do
  # Users table
  unless ActiveRecord::Base.connection.table_exists?(:users)
    create_table :users do |table|
      table.column :uuid, :string, null: false
      table.column :first_name, :string, null: false
      table.column :last_name, :string
      table.column :email, :string
    end
  end

  # Payment methods table
  unless ActiveRecord::Base.connection.table_exists?(:payment_methods)
    create_table :payment_methods do |table|
      table.references :user, null: false, foreign_key: true
      table.column :uuid, :string, null: false
      table.column :payment_type, :string, null: false # 'credit_card', 'paypal', 'bank_account'
      table.column :last_four, :string
      table.column :brand, :string # 'visa', 'mastercard', etc.
      table.column :is_default, :boolean, default: false
      table.column :status, :string, default: 'active' # 'active', 'expired', 'disabled'
      table.timestamps
    end
  end

  # Payments table
  unless ActiveRecord::Base.connection.table_exists?(:payments)
    create_table :payments do |table|
      table.references :user, null: false, foreign_key: true
      table.references :payment_method, null: false, foreign_key: true
      table.column :uuid, :string, null: false
      table.column :amount_cents, :integer, null: false
      table.column :currency, :string, default: 'USD'
      table.column :status, :string, null: false # 'pending', 'completed', 'failed', 'refunded'
      table.column :description, :text
      table.column :transaction_id, :string
      table.timestamps
    end
  end
end

# Define models
class User < ActiveRecord::Base
  has_many :payment_methods, dependent: :destroy
  has_many :payments, dependent: :destroy
end

class PaymentMethod < ActiveRecord::Base
  belongs_to :user
  has_many :payments, dependent: :destroy
end

class Payment < ActiveRecord::Base
  belongs_to :user
  belongs_to :payment_method
end

# Seed data only if tables are empty
if User.count == 0
  users_data = [
    { uuid: '1', first_name: 'John', last_name: 'Doe', email: '<EMAIL>' },
    { uuid: '2', first_name: 'Jane', last_name: 'Smith', email: '<EMAIL>' },
    { uuid: '3', first_name: 'Bob', last_name: 'Johnson', email: '<EMAIL>' },
    { uuid: '4', first_name: 'Alice', last_name: 'Brown', email: '<EMAIL>' },
    { uuid: '5', first_name: 'Charlie', last_name: 'Wilson', email: '<EMAIL>' }
  ]

  users_data.each do |user_data|
    User.create!(user_data)
  end
end

# Seed payment methods
if PaymentMethod.count == 0
  payment_methods_data = [
    { user_id: 1, uuid: 'pm_1', payment_type: 'credit_card', last_four: '4242', brand: 'visa', is_default: true },
    { user_id: 1, uuid: 'pm_2', payment_type: 'paypal', last_four: nil, brand: 'paypal', is_default: false },
    { user_id: 2, uuid: 'pm_3', payment_type: 'credit_card', last_four: '1234', brand: 'mastercard', is_default: true },
    { user_id: 3, uuid: 'pm_4', payment_type: 'bank_account', last_four: '5678', brand: 'bank', is_default: true }
  ]

  payment_methods_data.each do |pm_data|
    PaymentMethod.create!(pm_data)
  end
end

# Seed payments
if Payment.count == 0
  payments_data = [
    { user_id: 1, payment_method_id: 1, uuid: 'pay_1', amount_cents: 2999, status: 'completed', description: 'Monthly subscription', transaction_id: 'txn_123' },
    { user_id: 1, payment_method_id: 1, uuid: 'pay_2', amount_cents: 1500, status: 'completed', description: 'One-time purchase', transaction_id: 'txn_124' },
    { user_id: 2, payment_method_id: 3, uuid: 'pay_3', amount_cents: 5000, status: 'pending', description: 'Premium upgrade', transaction_id: 'txn_125' },
    { user_id: 3, payment_method_id: 4, uuid: 'pay_4', amount_cents: 999, status: 'failed', description: 'Small purchase', transaction_id: 'txn_126' }
  ]

  payments_data.each do |payment_data|
    Payment.create!(payment_data)
  end
end

puts "Database setup complete!"
puts "Created #{User.count} users, #{PaymentMethod.count} payment methods, #{Payment.count} payments."
