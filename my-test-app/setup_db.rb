#!/usr/bin/env ruby

require 'bundler'
Bundler.setup(:default, 'development')
Bundler.require(:default, 'development')

# Connect to SQLite database
ActiveRecord::Base.establish_connection(
  adapter:  'sqlite3',
  database: "development.sqlite3"
)

# Create the users table
ActiveRecord::Schema.define do
  create_table :users do |table|
    table.column :uuid, :string, null: false
    table.column :first_name, :string, null: false
    table.column :last_name, :string
    table.column :email, :string
  end
end

# Define the User model
class User < ActiveRecord::Base
end

# Seed some data
users_data = [
  { uuid: '1', first_name: '<PERSON>', last_name: '<PERSON><PERSON>', email: '<EMAIL>' },
  { uuid: '2', first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>' },
  { uuid: '3', first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>' },
  { uuid: '4', first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>' },
  { uuid: '5', first_name: '<PERSON>', last_name: '<PERSON>', email: 'char<PERSON>.w<PERSON><PERSON>@example.com' }
]

users_data.each do |user_data|
  User.create!(user_data)
end

puts "Database setup complete! Created #{User.count} users."
